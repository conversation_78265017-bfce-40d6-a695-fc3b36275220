'use client';

import { cn } from '@/src/app/_utils/utils';
import { OTPInput as InputOTP } from 'input-otp';
import { useRef } from 'react';

interface OTPInputProps {
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function OTPInput({
  value,
  onChange,
  maxLength = 6,
  disabled = false,
  error = false,
  className,
}: OTPInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div
      className={cn('flex w-full justify-center', className)}
      onClick={() => inputRef.current?.focus()}
    >
      <InputOTP
        ref={inputRef}
        maxLength={maxLength}
        value={value}
        onChange={onChange}
        disabled={disabled}
        containerClassName="flex items-center gap-3"
        className="group flex items-center"
        render={({ slots }) => {
          const firstEmptyIndex = slots.findIndex((slot) => !slot.char);
          return (
            <div className="flex items-center gap-3">
              {/* First 3 digits */}
              <div className="flex items-center gap-3">
                {slots.slice(0, 3).map((slot, index) => {
                  const isFilled = !!slot.char;
                  const isFocused =
                    index === firstEmptyIndex ||
                    (firstEmptyIndex === -1 && index === slots.length - 1);

                  return (
                    <div
                      key={index}
                      className={cn(
                        'flex h-14 w-12 items-center justify-center rounded-xl border-[1.5px] bg-[#F8FAFC] text-xl font-semibold text-[#0F172A] transition-all duration-200',
                        'border-[#E2E8F0]',
                        'focus-within:border-black focus-within:outline-none focus-within:ring-2 focus-within:ring-black/20',
                        isFilled && 'border-black bg-black/5',
                        isFocused &&
                          !isFilled &&
                          !disabled &&
                          'border-black12 ring-black12/20 ring-2',
                        // Error state
                        error && 'border-[#FF3B3B] bg-[#FF3B3B]/5',
                        // Disabled state
                        disabled && 'pointer-events-none bg-[#F1F5F9] opacity-40'
                      )}
                      style={{
                        fontFamily: 'Inter, system-ui, -apple-system, sans-serif',
                        fontSize: '20px',
                        lineHeight: '1',
                      }}
                    >
                      {slot.char ||
                        (index === firstEmptyIndex && !disabled ? (
                          <div
                            className="h-6 w-0.5 rounded-full bg-[#3B82F6]"
                            style={{
                              animation: 'caret-blink 1s ease-in-out infinite',
                            }}
                          />
                        ) : null)}
                    </div>
                  );
                })}
              </div>

              {/* Dot separator */}
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-[#64748B]"></div>
              </div>

              {/* Last 3 digits */}
              <div className="flex items-center gap-3">
                {slots.slice(3, 6).map((slot, index) => {
                  const actualIndex = index + 3;
                  const isFilled = !!slot.char;
                  const isFocused =
                    actualIndex === firstEmptyIndex ||
                    (firstEmptyIndex === -1 && actualIndex === slots.length - 1);

                  return (
                    <div
                      key={actualIndex}
                      className={cn(
                        'flex h-14 w-12 items-center justify-center rounded-xl border-[1.5px] bg-[#F8FAFC] text-xl font-semibold text-[#0F172A] transition-all duration-200',
                        'border-[#E2E8F0]',
                        'focus-within:border-black focus-within:outline-none focus-within:ring-2 focus-within:ring-black/20',
                        isFilled && 'border-black bg-black/5',
                        isFocused && !isFilled && !disabled && 'border-black ring-2 ring-black/20',
                        error && 'border-[#FF3B3B] bg-[#FF3B3B]/5',
                        disabled && 'pointer-events-none bg-[#F1F5F9] opacity-40'
                      )}
                      style={{
                        fontFamily: 'Inter, system-ui, -apple-system, sans-serif',
                        fontSize: '20px',
                        lineHeight: '1',
                      }}
                    >
                      {slot.char ||
                        (actualIndex === firstEmptyIndex && !disabled ? (
                          <div
                            className="h-6 w-0.5 rounded-full bg-black"
                            style={{
                              animation: 'caret-blink 1s ease-in-out infinite',
                            }}
                          />
                        ) : null)}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        }}
      />
    </div>
  );
}
